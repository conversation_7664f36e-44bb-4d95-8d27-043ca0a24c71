import React, { useState } from 'react';
import { Layout, Menu, Typography, Card, Row, Col, Statistic } from 'antd';
import {
  UserOutlined,
  DashboardOutlined,
  DatabaseOutlined,
  RobotOutlined,
  BarChartOutlined,
  SettingOutlined,
  TeamOutlined
} from '@ant-design/icons';

import CrudModule from '@/modules/CrudModule/CrudModule';
import AdminForm from '@/forms/AdminForm';
import SystemOverview from '@/components/AdminDashboard/SystemOverview';
import DatabaseManagement from '@/components/AdminDashboard/DatabaseManagement';
import AIServicesConfig from '@/components/AdminDashboard/AIServicesConfig';
import useLanguage from '@/locale/useLanguage';

const { Content, Sider } = Layout;
const { Title } = Typography;

export default function Admin() {
  const [selectedKey, setSelectedKey] = useState('dashboard');
  const translate = useLanguage();

  const entity = 'admin';
  const searchConfig = {
    displayLabels: ['name', 'email'],
    searchFields: 'name,email',
  };
  const deleteModalLabels = ['name', 'email'];

  const Labels = {
    PANEL_TITLE: translate('admin'),
    DATATABLE_TITLE: translate('admin_list'),
    ADD_NEW_ENTITY: translate('add_new_admin'),
    ENTITY_NAME: translate('admin'),
  };
  const configPage = {
    entity,
    ...Labels,
  };
  const config = {
    ...configPage,
    searchConfig,
    deleteModalLabels,
  };

  const menuItems = [
    {
      key: 'dashboard',
      icon: <DashboardOutlined />,
      label: 'Przegląd Systemu',
    },
    {
      key: 'database',
      icon: <DatabaseOutlined />,
      label: 'Baza Danych',
    },
    {
      key: 'ai-services',
      icon: <RobotOutlined />,
      label: 'Usługi AI',
    },
    {
      key: 'users',
      icon: <TeamOutlined />,
      label: 'Zarządzanie Użytkownikami',
    },
    {
      key: 'analytics',
      icon: <BarChartOutlined />,
      label: 'Analityka',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: 'Ustawienia',
    },
  ];

  const renderContent = () => {
    switch (selectedKey) {
      case 'dashboard':
        return <SystemOverview />;
      case 'database':
        return <DatabaseManagement />;
      case 'ai-services':
        return <AIServicesConfig />;
      case 'users':
        return (
          <CrudModule
            createForm={<AdminForm isUpdateForm={false} />}
            updateForm={<AdminForm isUpdateForm={true} />}
            config={config}
          />
        );
      case 'analytics':
        return (
          <div style={{ padding: '24px' }}>
            <Title level={2}>📊 Analityka Systemu</Title>
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12} md={6}>
                <Card>
                  <Statistic
                    title="Aktywni Użytkownicy (7 dni)"
                    value={42}
                    prefix={<UserOutlined />}
                  />
                </Card>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Card>
                  <Statistic
                    title="Zapytania API (24h)"
                    value={1250}
                    prefix={<BarChartOutlined />}
                  />
                </Card>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Card>
                  <Statistic
                    title="Przetworzonych Emaili"
                    value={89}
                    prefix={<DatabaseOutlined />}
                  />
                </Card>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Card>
                  <Statistic
                    title="AI Analiz"
                    value={156}
                    prefix={<RobotOutlined />}
                  />
                </Card>
              </Col>
            </Row>
          </div>
        );
      case 'settings':
        return (
          <div style={{ padding: '24px' }}>
            <Title level={2}>⚙️ Ustawienia Systemu</Title>
            <Card>
              <p>Panel ustawień systemu będzie dostępny wkrótce...</p>
            </Card>
          </div>
        );
      default:
        return <SystemOverview />;
    }
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider width={250} theme="light" style={{ borderRight: '1px solid #f0f0f0' }}>
        <div style={{ padding: '16px', borderBottom: '1px solid #f0f0f0' }}>
          <Title level={4} style={{ margin: 0, textAlign: 'center' }}>
            🔧 Admin Panel
          </Title>
        </div>
        <Menu
          mode="inline"
          selectedKeys={[selectedKey]}
          items={menuItems}
          onClick={({ key }) => setSelectedKey(key)}
          style={{ border: 'none' }}
        />
      </Sider>
      <Layout>
        <Content style={{ background: '#f5f5f5' }}>
          {renderContent()}
        </Content>
      </Layout>
    </Layout>
  );
}
