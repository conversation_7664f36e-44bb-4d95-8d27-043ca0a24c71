import React from 'react';
import { Card, Timeline, Typography, Avatar, Tag, Tooltip, Spin } from 'antd';
import { UserOutlined, ClockCircleOutlined, CheckCircleOutlined, CloseCircleOutlined, EnvironmentOutlined } from '@ant-design/icons';
import { CosmicCard } from '../cosmic/index';

const { Text, Title } = Typography;

const CustomerJourneyMap = ({ customerId, customer360Data }) => {
  const { interactionHistory, predictiveInsights } = customer360Data || {};

  // Loading state
  if (!customer360Data) {
    return (
      <CosmicCard className="animate-fade-in" style={{ textAlign: 'center', padding: 'var(--space-xxl)' }}>
        <div className="animate-scale-in">
          <Spin size="large" />
          <Title level={4} style={{ marginTop: 'var(--space-md)', color: 'var(--hvac-primary)' }}>
            🚀 Ładowanie Mapy Podróży Klienta
          </Title>
          <Text type="secondary">
            <PERSON><PERSON><PERSON><PERSON><PERSON> dane klienta...
          </Text>
        </div>
      </CosmicCard>
    );
  }

  // Generate journey stages from interaction history
  const journeyStages = interactionHistory?.recentInteractions?.map((interaction, index) => ({
    id: index,
    title: interaction.type.charAt(0).toUpperCase() + interaction.type.slice(1),
    description: interaction.summary,
    date: new Date(interaction.date).toLocaleString('pl-PL'),
    sentiment: interaction.sentiment,
    outcome: interaction.outcome,
    duration: interaction.duration,
    priority: interaction.priority
  })) || [];

  // Add predictive insights as future stages
  if (predictiveInsights?.nextBestActions) {
    predictiveInsights.nextBestActions.forEach((action, index) => {
      journeyStages.push({
        id: journeyStages.length + index,
        title: 'AI Recommended Action',
        description: action.action,
        date: 'Planned: ' + action.timing,
        sentiment: action.priority === 'high' ? 'urgent' : 'positive',
        outcome: action.expectedOutcome,
        priority: action.priority
      });
    });
  }

  return (
    <div style={{ padding: 'var(--space-lg)', background: 'var(--hvac-gray-50)', minHeight: '100vh' }}>
      <CosmicCard
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: 'var(--space-sm)' }}>
            <EnvironmentOutlined style={{ color: 'var(--hvac-primary)' }} />
            🗺️ Mapa Podróży Klienta
          </div>
        }
        extra={
          <Tag color="blue" icon={<UserOutlined />}>
            {customer360Data.basicInfo?.name || 'Klient'}
          </Tag>
        }
        className="animate-fade-in"
      >
        <Timeline mode="alternate" style={{ marginTop: 'var(--space-md)' }}>
          {journeyStages.map((stage, index) => (
            <Timeline.Item
              key={stage.id}
              color={stage.sentiment === 'positive' ? 'green' : stage.sentiment === 'urgent' ? 'red' : 'blue'}
              dot={
                <Avatar
                  style={{
                    backgroundColor: stage.sentiment === 'positive' ? 'var(--hvac-success)' :
                                    stage.sentiment === 'urgent' ? 'var(--hvac-error)' :
                                    'var(--hvac-primary)',
                    border: '2px solid white'
                  }}
                  icon={<ClockCircleOutlined />}
                />
              }
              className="animate-slide-in"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <CosmicCard
                size="small"
                hoverable
                style={{
                  borderLeft: `4px solid ${stage.sentiment === 'positive' ? 'var(--hvac-success)' :
                                                  stage.sentiment === 'urgent' ? 'var(--hvac-error)' :
                                                  'var(--hvac-primary)'}`
                }}
              >
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                  <div style={{ flex: 1 }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: 'var(--space-sm)', marginBottom: 'var(--space-xs)' }}>
                      <Title level={5} style={{ margin: 0, color: 'var(--hvac-primary)' }}>
                        {stage.title}
                      </Title>
                      {stage.priority && (
                        <Tag
                          color={stage.priority === 'high' ? 'red' : 'orange'}
                          icon={stage.priority === 'high' ? <CloseCircleOutlined /> : <CheckCircleOutlined />}
                        >
                          {stage.priority}
                        </Tag>
                      )}
                    </div>

                    <Text type="secondary" style={{ marginBottom: 'var(--space-sm)' }}>
                      {stage.date}
                    </Text>

                    <Text ellipsis={{ rows: 2, expandable: true }} style={{ marginBottom: 'var(--space-sm)' }}>
                      {stage.description}
                    </Text>

                    {stage.outcome && (
                      <Tooltip title={stage.outcome}>
                        <Tag color="blue" icon={<CheckCircleOutlined />}>
                          {stage.outcome}
                        </Tag>
                      </Tooltip>
                    )}

                    {stage.duration && (
                      <Tag icon={<ClockCircleOutlined />} color="green">
                        {stage.duration}
                      </Tag>
                    )}
                  </div>
                </div>
              </CosmicCard>
            </Timeline.Item>
          ))}
        </Timeline>
      </CosmicCard>
    </div>
  );
};

export default CustomerJourneyMap;
