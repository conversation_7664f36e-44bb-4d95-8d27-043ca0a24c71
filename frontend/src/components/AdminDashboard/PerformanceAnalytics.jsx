/**
 * ⚡ PERFORMANCE ANALYTICS COMPONENT
 * 
 * Advanced performance monitoring and analytics for Fulmark HVAC CRM
 * Real-time charts, trends analysis, and performance optimization insights
 */

import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Row, 
  Col, 
  Statistic, 
  Progress, 
  Alert, 
  Spin, 
  Typography, 
  Tabs,
  Button,
  Space,
  Tag,
  Table,
  Timeline
} from 'antd';
import { 
  ThunderboltOutlined, 
  ClockCircleOutlined, 
  ApiOutlined, 
  DatabaseOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  ReloadOutlined,
  TrendingUpOutlined,
  TrendingDownOutlined
} from '@ant-design/icons';
import { Line, Area, Column } from '@ant-design/plots';
import { request } from '@/request';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

const PerformanceAnalytics = () => {
  const [loading, setLoading] = useState(true);
  const [performanceData, setPerformanceData] = useState(null);
  const [error, setError] = useState(null);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchPerformanceData();
    // Auto-refresh every 30 seconds
    const interval = setInterval(fetchPerformanceData, 30000);
    return () => clearInterval(interval);
  }, []);

  const fetchPerformanceData = async () => {
    try {
      setLoading(true);
      const response = await request.get('/admin/performance-metrics');
      setPerformanceData(response.result);
      setError(null);
    } catch (err) {
      console.error('Failed to fetch performance data:', err);
      setError('Nie udało się pobrać danych wydajności');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchPerformanceData();
    setRefreshing(false);
  };

  const getPerformanceColor = (score) => {
    if (score >= 90) return '#52c41a';
    if (score >= 70) return '#faad14';
    if (score >= 50) return '#fa8c16';
    return '#f5222d';
  };

  const formatResponseTime = (time) => {
    if (time < 1000) return `${time}ms`;
    return `${(time / 1000).toFixed(2)}s`;
  };

  // Prepare chart data
  const prepareChartData = () => {
    if (!performanceData?.trends?.dataPoints) return [];
    
    return performanceData.trends.dataPoints.map(point => ({
      time: new Date(point.timestamp).toLocaleTimeString('pl-PL', { 
        hour: '2-digit', 
        minute: '2-digit' 
      }),
      responseTime: point.responseTime,
      requests: point.requestCount,
      errorRate: point.errorRate,
      cpuUsage: point.cpuUsage,
      memoryUsage: point.memoryUsage
    }));
  };

  const chartData = prepareChartData();

  // API Endpoints performance table
  const endpointsColumns = [
    {
      title: 'Endpoint',
      dataIndex: 'endpoint',
      key: 'endpoint',
      render: (text) => <Text code>{text}</Text>,
    },
    {
      title: 'Średni Czas',
      dataIndex: 'avgTime',
      key: 'avgTime',
      render: (time) => formatResponseTime(time),
      sorter: (a, b) => a.avgTime - b.avgTime,
    },
    {
      title: 'Zapytania',
      dataIndex: 'requests',
      key: 'requests',
      render: (count) => count?.toLocaleString(),
      sorter: (a, b) => a.requests - b.requests,
    },
    {
      title: 'Status',
      key: 'status',
      render: (_, record) => {
        const isGood = record.avgTime < 200;
        return (
          <Tag color={isGood ? 'green' : record.avgTime < 500 ? 'orange' : 'red'}>
            {isGood ? 'Dobry' : record.avgTime < 500 ? 'Średni' : 'Wolny'}
          </Tag>
        );
      },
    },
  ];

  const endpointsData = performanceData?.metrics?.api?.endpoints ? 
    Object.entries(performanceData.metrics.api.endpoints).map(([endpoint, data]) => ({
      key: endpoint,
      endpoint,
      avgTime: data.avgTime,
      requests: data.requests
    })) : [];

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>
          <Text>Ładowanie danych wydajności...</Text>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message="Błąd wydajności"
        description={error}
        type="error"
        showIcon
        action={
          <Button onClick={fetchPerformanceData}>
            Spróbuj ponownie
          </Button>
        }
      />
    );
  }

  const { performanceScore, metrics, trends, analysis } = performanceData;

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <Title level={2}>
          ⚡ Analityka Wydajności
        </Title>
        <Button 
          icon={<ReloadOutlined />} 
          onClick={handleRefresh}
          loading={refreshing}
        >
          Odśwież
        </Button>
      </div>

      {/* Performance Overview */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Wynik Wydajności"
              value={performanceScore || 0}
              suffix="%"
              valueStyle={{ color: getPerformanceColor(performanceScore || 0) }}
              prefix={<ThunderboltOutlined />}
            />
            <Progress 
              percent={performanceScore || 0} 
              strokeColor={getPerformanceColor(performanceScore || 0)}
              showInfo={false}
              size="small"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Średni Czas Odpowiedzi"
              value={metrics?.api?.averageResponseTime || 0}
              suffix="ms"
              prefix={<ClockCircleOutlined />}
              valueStyle={{ 
                color: (metrics?.api?.averageResponseTime || 0) < 200 ? '#52c41a' : '#faad14' 
              }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Zapytania/Sekunda"
              value={metrics?.api?.requestsPerSecond || 0}
              precision={1}
              prefix={<ApiOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Wskaźnik Sukcesu"
              value={metrics?.api?.successRate || 0}
              suffix="%"
              prefix={<CheckCircleOutlined />}
              valueStyle={{ 
                color: (metrics?.api?.successRate || 0) >= 99 ? '#52c41a' : '#faad14' 
              }}
            />
          </Card>
        </Col>
      </Row>

      <Tabs defaultActiveKey="trends">
        <TabPane tab="Trendy Wydajności" key="trends">
          <Row gutter={[16, 16]}>
            <Col xs={24} lg={12}>
              <Card title="Czas Odpowiedzi (24h)" size="small">
                <Line
                  data={chartData}
                  xField="time"
                  yField="responseTime"
                  height={200}
                  smooth={true}
                  color="#1890ff"
                  point={{ size: 3 }}
                  tooltip={{
                    formatter: (datum) => ({
                      name: 'Czas odpowiedzi',
                      value: `${datum.responseTime}ms`
                    })
                  }}
                />
              </Card>
            </Col>
            <Col xs={24} lg={12}>
              <Card title="Liczba Zapytań (24h)" size="small">
                <Area
                  data={chartData}
                  xField="time"
                  yField="requests"
                  height={200}
                  color="#52c41a"
                  areaStyle={{ fillOpacity: 0.3 }}
                  tooltip={{
                    formatter: (datum) => ({
                      name: 'Zapytania',
                      value: datum.requests
                    })
                  }}
                />
              </Card>
            </Col>
            <Col xs={24} lg={12}>
              <Card title="Użycie CPU (24h)" size="small">
                <Line
                  data={chartData}
                  xField="time"
                  yField="cpuUsage"
                  height={200}
                  smooth={true}
                  color="#fa8c16"
                  point={{ size: 3 }}
                  tooltip={{
                    formatter: (datum) => ({
                      name: 'CPU',
                      value: `${datum.cpuUsage.toFixed(1)}%`
                    })
                  }}
                />
              </Card>
            </Col>
            <Col xs={24} lg={12}>
              <Card title="Użycie Pamięci (24h)" key="memory">
                <Line
                  data={chartData}
                  xField="time"
                  yField="memoryUsage"
                  height={200}
                  smooth={true}
                  color="#722ed1"
                  point={{ size: 3 }}
                  tooltip={{
                    formatter: (datum) => ({
                      name: 'Pamięć',
                      value: `${datum.memoryUsage.toFixed(1)}%`
                    })
                  }}
                />
              </Card>
            </Col>
          </Row>
        </TabPane>

        <TabPane tab="Endpointy API" key="endpoints">
          <Card>
            <Table
              columns={endpointsColumns}
              dataSource={endpointsData}
              pagination={{ pageSize: 10 }}
              size="small"
            />
          </Card>
        </TabPane>

        <TabPane tab="Analiza Wąskich Gardeł" key="bottlenecks">
          <Row gutter={[16, 16]}>
            <Col xs={24} lg={12}>
              <Card title="Zidentyfikowane Problemy" size="small">
                {analysis?.bottlenecks?.length > 0 ? (
                  <Timeline>
                    {analysis.bottlenecks.map((bottleneck, index) => (
                      <Timeline.Item
                        key={index}
                        color={bottleneck.severity === 'high' ? 'red' : 
                               bottleneck.severity === 'medium' ? 'orange' : 'blue'}
                        dot={bottleneck.severity === 'high' ? <WarningOutlined /> : undefined}
                      >
                        <div>
                          <Text strong>{bottleneck.type}</Text>
                          <br />
                          <Text type="secondary">{bottleneck.description}</Text>
                        </div>
                      </Timeline.Item>
                    ))}
                  </Timeline>
                ) : (
                  <div style={{ textAlign: 'center', padding: '20px' }}>
                    <CheckCircleOutlined style={{ fontSize: '48px', color: '#52c41a' }} />
                    <div style={{ marginTop: '16px' }}>
                      <Text>Nie wykryto problemów z wydajnością!</Text>
                    </div>
                  </div>
                )}
              </Card>
            </Col>
            <Col xs={24} lg={12}>
              <Card title="Rekomendacje" size="small">
                {analysis?.recommendations?.length > 0 ? (
                  <ul style={{ paddingLeft: '20px' }}>
                    {analysis.recommendations.map((rec, index) => (
                      <li key={index} style={{ marginBottom: '8px' }}>
                        <Text>{rec}</Text>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <div style={{ textAlign: 'center', padding: '20px' }}>
                    <Text type="secondary">Brak rekomendacji - system działa optymalnie</Text>
                  </div>
                )}
              </Card>
            </Col>
          </Row>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default PerformanceAnalytics;
