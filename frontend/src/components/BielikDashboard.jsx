import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
    Brain, 
    Activity, 
    CheckCircle, 
    XCircle, 
    AlertTriangle,
    Zap,
    Clock,
    BarChart3,
    RefreshCw,
    TestTube
} from 'lucide-react';

/**
 * 🇵🇱 BIELIK V3 4.5B DASHBOARD
 * Monitoring and testing interface for Polish HVAC AI
 */
const BielikDashboard = () => {
    const [health, setHealth] = useState(null);
    const [stats, setStats] = useState(null);
    const [testResult, setTestResult] = useState(null);
    const [testPrompt, setTestPrompt] = useState(
        'Przeanalizuj następujące zgłoszenie serwisowe HVAC: "Klimatyzacja w biurze nie chł<PERSON>zi, robi dziwne dźwięki i kapie woda. Pilne." Określ priorytet, typ problemu i sugerowane działania.'
    );
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    useEffect(() => {
        fetchData();
        const interval = setInterval(fetchData, 30000); // Refresh every 30 seconds
        return () => clearInterval(interval);
    }, []);

    const fetchData = async () => {
        try {
            const [healthRes, statsRes] = await Promise.all([
                fetch('/api/ai/health'),
                fetch('/api/ai/stats')
            ]);

            if (healthRes.ok) {
                const healthData = await healthRes.json();
                setHealth(healthData.data);
            }

            if (statsRes.ok) {
                const statsData = await statsRes.json();
                setStats(statsData.data);
            }
        } catch (err) {
            console.error('Failed to fetch AI data:', err);
            setError('Nie udało się pobrać danych AI');
        }
    };

    const testBielik = async () => {
        setLoading(true);
        setError(null);
        try {
            const response = await fetch('/api/ai/test/bielik', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ prompt: testPrompt })
            });

            const data = await response.json();
            if (data.success) {
                setTestResult(data.data);
            } else {
                setError(data.error || 'Test Bielik nie powiódł się');
            }
        } catch (err) {
            console.error('Bielik test failed:', err);
            setError('Błąd podczas testowania Bielik');
        } finally {
            setLoading(false);
        }
    };

    const resetStats = async () => {
        try {
            const response = await fetch('/api/ai/stats/reset', {
                method: 'POST'
            });
            if (response.ok) {
                fetchData();
            }
        } catch (err) {
            console.error('Failed to reset stats:', err);
            notification.error({
                message: '❌ Błąd Resetu Statystyk',
                description: 'Wystąpił problem podczas resetu statystyk. Spróbuj ponownie później.',
                duration: 5
            });
        }
    };

    const getStatusIcon = (status) => {
        switch (status) {
            case 'healthy':
                return <CheckCircle className="h-5 w-5 text-green-500" />;
            case 'degraded':
                return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
            case 'critical':
            case 'unhealthy':
                return <XCircle className="h-5 w-5 text-red-500" />;
            default:
                return <Activity className="h-5 w-5 text-gray-500" />;
        }
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'healthy':
                return 'bg-green-100 text-green-800';
            case 'degraded':
                return 'bg-yellow-100 text-yellow-800';
            case 'critical':
            case 'unhealthy':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    return (
        <div className="p-6 space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                    <Brain className="h-8 w-8 text-blue-600" />
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">
                            🇵🇱 Bielik V3 4.5B Dashboard
                        </h1>
                        <p className="text-gray-600">
                            Polski AI dla HVAC - Monitoring i Testowanie
                        </p>
                    </div>
                </div>
                <Button onClick={fetchData} variant="outline" size="sm">
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Odśwież
                </Button>
            </div>

            {error && (
                <Alert variant="destructive">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                </Alert>
            )}

            {/* Overall Status */}
            {health && (
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            {getStatusIcon(health.overall_status)}
                            <span>Status Systemu AI</span>
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="flex items-center justify-between">
                            <Badge className={getStatusColor(health.overall_status)}>
                                {health.overall_status.toUpperCase()}
                            </Badge>
                            <div className="text-sm text-gray-500">
                                Priorytet: {health.priority?.join(' → ')}
                            </div>
                        </div>
                    </CardContent>
                </Card>
            )}

            {/* Models Status */}
            {health?.models && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {Object.entries(health.models).map(([modelName, modelHealth]) => (
                        <Card key={modelName}>
                            <CardHeader className="pb-3">
                                <CardTitle className="flex items-center justify-between text-sm">
                                    <span className="capitalize">{modelName}</span>
                                    {getStatusIcon(modelHealth.status)}
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="pt-0">
                                <div className="space-y-2">
                                    <Badge 
                                        size="sm" 
                                        className={getStatusColor(modelHealth.status)}
                                    >
                                        {modelHealth.status}
                                    </Badge>
                                    {modelHealth.enabled !== undefined && (
                                        <div className="text-xs text-gray-600">
                                            Włączony: {modelHealth.enabled ? 'Tak' : 'Nie'}
                                        </div>
                                    )}
                                    {modelHealth.model && (
                                        <div className="text-xs text-gray-600">
                                            Model: {modelHealth.model}
                                        </div>
                                    )}
                                    {modelHealth.error && (
                                        <div className="text-xs text-red-600">
                                            Błąd: {modelHealth.error}
                                        </div>
                                    )}
                                </div>
                            </CardContent>
                        </Card>
                    ))}
                </div>
            )}

            {/* Statistics */}
            {stats && (
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <Card>
                        <CardContent className="p-4">
                            <div className="flex items-center space-x-2">
                                <BarChart3 className="h-5 w-5 text-blue-500" />
                                <div>
                                    <div className="text-2xl font-bold">{stats.requests}</div>
                                    <div className="text-sm text-gray-600">Zapytania</div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-4">
                            <div className="flex items-center space-x-2">
                                <CheckCircle className="h-5 w-5 text-green-500" />
                                <div>
                                    <div className="text-2xl font-bold">{stats.successes}</div>
                                    <div className="text-sm text-gray-600">Sukcesy</div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-4">
                            <div className="flex items-center space-x-2">
                                <XCircle className="h-5 w-5 text-red-500" />
                                <div>
                                    <div className="text-2xl font-bold">{stats.failures}</div>
                                    <div className="text-sm text-gray-600">Błędy</div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-4">
                            <div className="flex items-center space-x-2">
                                <Clock className="h-5 w-5 text-purple-500" />
                                <div>
                                    <div className="text-2xl font-bold">
                                        {Math.round(stats.avg_response_time)}ms
                                    </div>
                                    <div className="text-sm text-gray-600">Śr. czas</div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            )}

            {/* Model Usage Statistics */}
            {stats?.model_usage && (
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center justify-between">
                            <span>Statystyki Użycia Modeli</span>
                            <Button onClick={resetStats} variant="outline" size="sm">
                                Reset
                            </Button>
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {Object.entries(stats.model_usage).map(([model, usage]) => (
                                <div key={model} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div className="flex items-center space-x-3">
                                        <span className="font-medium capitalize">{model}</span>
                                        <Badge variant="outline">
                                            {usage.requests} zapytań
                                        </Badge>
                                    </div>
                                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                                        <span>Sukcesy: {usage.successes}</span>
                                        <span>Błędy: {usage.failures}</span>
                                        <span>Śr. czas: {Math.round(usage.avg_time)}ms</span>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </CardContent>
                </Card>
            )}

            {/* Bielik Test */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                        <TestTube className="h-5 w-5" />
                        <span>Test Bielik V3 4.5B</span>
                    </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div>
                        <label className="block text-sm font-medium mb-2">
                            Prompt testowy (polski HVAC):
                        </label>
                        <Textarea
                            value={testPrompt}
                            onChange={(e) => setTestPrompt(e.target.value)}
                            rows={3}
                            placeholder="Wprowadź tekst do analizy..."
                        />
                    </div>
                    
                    <Button 
                        onClick={testBielik} 
                        disabled={loading || !testPrompt.trim()}
                        className="w-full"
                    >
                        {loading ? (
                            <>
                                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                                Testowanie...
                            </>
                        ) : (
                            <>
                                <Zap className="h-4 w-4 mr-2" />
                                Testuj Bielik
                            </>
                        )}
                    </Button>

                    {testResult && (
                        <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                            <h4 className="font-medium text-green-800 mb-2">
                                Wynik analizy Bielik V3 4.5B:
                            </h4>
                            <div className="space-y-2 text-sm">
                                <div><strong>Model:</strong> {testResult.model_used}</div>
                                <div><strong>Czas odpowiedzi:</strong> {testResult.response_time}ms</div>
                                <div><strong>Analiza:</strong> {testResult.analiza}</div>
                                <div><strong>Kategoria:</strong> {testResult.kategoria}</div>
                                <div><strong>Priorytet:</strong> {testResult.priorytet}</div>
                                <div><strong>Sentiment:</strong> {testResult.sentiment}</div>
                                {testResult.akcje && (
                                    <div><strong>Akcje:</strong> {Array.isArray(testResult.akcje) ? testResult.akcje.join(', ') : testResult.akcje}</div>
                                )}
                            </div>
                        </div>
                    )}
                </CardContent>
            </Card>
        </div>
    );
};

export default BielikDashboard;
