/**
 * 🤖 AI SERVICES CONFIGURATION COMPONENT
 * 
 * Comprehensive AI services management for Fulmark HVAC CRM
 * Bielik V3, Email Intelligence, Weaviate configuration and monitoring
 */

import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Row, 
  Col, 
  Statistic, 
  Badge, 
  Alert, 
  Spin, 
  Typography, 
  Tabs,
  Button,
  Space,
  Progress,
  Tag,
  Descriptions,
  Switch
} from 'antd';
import { 
  RobotOutlined, 
  CloudServerOutlined, 
  DatabaseOutlined, 
  MailOutlined,
  ApiOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined,
  ReloadOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { request } from '@/request';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

const AIServicesConfig = () => {
  const [loading, setLoading] = useState(true);
  const [aiData, setAiData] = useState(null);
  const [error, setError] = useState(null);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchAIData();
    // Auto-refresh every 60 seconds
    const interval = setInterval(fetchAIData, 60000);
    return () => clearInterval(interval);
  }, []);

  const fetchAIData = async () => {
    try {
      setLoading(true);
      const response = await request.get('/admin/ai-services-status');
      setAiData(response.result);
      setError(null);
    } catch (err) {
      console.error('Failed to fetch AI data:', err);
      setError('Nie udało się pobrać danych usług AI');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchAIData();
    setRefreshing(false);
  };

  const getServiceStatusColor = (status) => {
    switch (status) {
      case 'healthy':
      case 'configured':
        return 'success';
      case 'unhealthy':
        return 'warning';
      case 'error':
        return 'error';
      case 'disabled':
        return 'default';
      default:
        return 'default';
    }
  };

  const getServiceStatusIcon = (status) => {
    switch (status) {
      case 'healthy':
      case 'configured':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'unhealthy':
        return <ExclamationCircleOutlined style={{ color: '#faad14' }} />;
      case 'error':
        return <CloseCircleOutlined style={{ color: '#f5222d' }} />;
      default:
        return <ExclamationCircleOutlined style={{ color: '#d9d9d9' }} />;
    }
  };

  const getServiceStatusText = (status) => {
    switch (status) {
      case 'healthy':
        return 'Działa Prawidłowo';
      case 'configured':
        return 'Skonfigurowane';
      case 'unhealthy':
        return 'Problemy';
      case 'error':
        return 'Błąd';
      case 'disabled':
        return 'Wyłączone';
      default:
        return 'Nieznany';
    }
  };

  const getHealthColor = (score) => {
    if (score >= 90) return '#52c41a';
    if (score >= 70) return '#faad14';
    if (score >= 50) return '#fa8c16';
    return '#f5222d';
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>
          <Text>Ładowanie danych usług AI...</Text>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message="Błąd usług AI"
        description={error}
        type="error"
        showIcon
        action={
          <Button onClick={fetchAIData}>
            Spróbuj ponownie
          </Button>
        }
      />
    );
  }

  const { healthScore, configuration, services, performance, usage, capabilities } = aiData;

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <Title level={2}>
          🤖 Konfiguracja Usług AI
        </Title>
        <Button 
          icon={<ReloadOutlined />} 
          onClick={handleRefresh}
          loading={refreshing}
        >
          Odśwież
        </Button>
      </div>

      {/* AI Health Overview */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Stan Usług AI"
              value={healthScore || 0}
              suffix="%"
              valueStyle={{ color: getHealthColor(healthScore || 0) }}
              prefix={<RobotOutlined />}
            />
            <Progress 
              percent={healthScore || 0} 
              strokeColor={getHealthColor(healthScore || 0)}
              showInfo={false}
              size="small"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Średni Czas Odpowiedzi"
              value={performance?.averageResponseTime || 0}
              suffix="ms"
              prefix={<CloudServerOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Zapytania/Godzina"
              value={performance?.requestsPerHour || 0}
              prefix={<ApiOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Wskaźnik Sukcesu"
              value={performance?.successRate || 0}
              suffix="%"
              valueStyle={{ color: performance?.successRate >= 95 ? '#52c41a' : '#faad14' }}
              prefix={<CheckCircleOutlined />}
            />
          </Card>
        </Col>
      </Row>

      <Tabs defaultActiveKey="services">
        <TabPane tab="Usługi AI" key="services">
          <Row gutter={[16, 16]}>
            {services && Object.entries(services).map(([serviceName, serviceData]) => (
              <Col xs={24} sm={12} lg={8} key={serviceName}>
                <Card 
                  title={
                    <Space>
                      {getServiceStatusIcon(serviceData.status)}
                      <Text strong>
                        {serviceName === 'bielik' ? 'Bielik V3' :
                         serviceName === 'emailIntelligence' ? 'Email Intelligence' :
                         serviceName === 'weaviate' ? 'Weaviate Vector DB' :
                         serviceName === 'lmStudio' ? 'LM Studio' :
                         serviceName === 'openai' ? 'OpenAI GPT-4' : serviceName}
                      </Text>
                    </Space>
                  }
                  size="small"
                  extra={
                    <Badge 
                      status={getServiceStatusColor(serviceData.status)} 
                      text={getServiceStatusText(serviceData.status)} 
                    />
                  }
                >
                  <Descriptions column={1} size="small">
                    <Descriptions.Item label="Status">
                      <Tag color={getServiceStatusColor(serviceData.status)}>
                        {getServiceStatusText(serviceData.status)}
                      </Tag>
                    </Descriptions.Item>
                    
                    {serviceData.responseTime && (
                      <Descriptions.Item label="Czas Odpowiedzi">
                        {serviceData.responseTime}ms
                      </Descriptions.Item>
                    )}
                    
                    {serviceData.url && (
                      <Descriptions.Item label="URL">
                        <Text code style={{ fontSize: '11px' }}>{serviceData.url}</Text>
                      </Descriptions.Item>
                    )}
                    
                    {serviceData.modelLoaded !== undefined && (
                      <Descriptions.Item label="Model Załadowany">
                        <Badge 
                          status={serviceData.modelLoaded ? 'success' : 'error'} 
                          text={serviceData.modelLoaded ? 'Tak' : 'Nie'} 
                        />
                      </Descriptions.Item>
                    )}
                    
                    {serviceData.models && (
                      <Descriptions.Item label="Dostępne Modele">
                        {serviceData.models}
                      </Descriptions.Item>
                    )}
                    
                    {serviceData.schema && (
                      <Descriptions.Item label="Klasy Schema">
                        {serviceData.schema.classes || 0}
                      </Descriptions.Item>
                    )}
                    
                    {serviceData.error && (
                      <Descriptions.Item label="Błąd">
                        <Text type="danger" style={{ fontSize: '11px' }}>
                          {serviceData.error}
                        </Text>
                      </Descriptions.Item>
                    )}
                  </Descriptions>
                </Card>
              </Col>
            ))}
          </Row>
        </TabPane>

        <TabPane tab="Możliwości" key="capabilities">
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={8}>
              <Card title="Analiza Tekstu" size="small">
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '48px', marginBottom: '16px' }}>
                    📝
                  </div>
                  <Badge 
                    status={capabilities?.textAnalysis ? 'success' : 'error'} 
                    text={capabilities?.textAnalysis ? 'Dostępne' : 'Niedostępne'} 
                  />
                  <div style={{ marginTop: '8px' }}>
                    <Text type="secondary">
                      Analiza tekstu z Bielik V3 lub OpenAI
                    </Text>
                  </div>
                </div>
              </Card>
            </Col>
            
            <Col xs={24} sm={12} md={8}>
              <Card title="Przetwarzanie Email" size="small">
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '48px', marginBottom: '16px' }}>
                    📧
                  </div>
                  <Badge 
                    status={capabilities?.emailProcessing ? 'success' : 'error'} 
                    text={capabilities?.emailProcessing ? 'Dostępne' : 'Niedostępne'} 
                  />
                  <div style={{ marginTop: '8px' }}>
                    <Text type="secondary">
                      Inteligentne przetwarzanie emaili
                    </Text>
                  </div>
                </div>
              </Card>
            </Col>
            
            <Col xs={24} sm={12} md={8}>
              <Card title="Wyszukiwanie Wektorowe" size="small">
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '48px', marginBottom: '16px' }}>
                    🔍
                  </div>
                  <Badge 
                    status={capabilities?.vectorSearch ? 'success' : 'error'} 
                    text={capabilities?.vectorSearch ? 'Dostępne' : 'Niedostępne'} 
                  />
                  <div style={{ marginTop: '8px' }}>
                    <Text type="secondary">
                      Semantyczne wyszukiwanie z Weaviate
                    </Text>
                  </div>
                </div>
              </Card>
            </Col>
            
            <Col xs={24} sm={12} md={8}>
              <Card title="AI Konwersacyjne" size="small">
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '48px', marginBottom: '16px' }}>
                    💬
                  </div>
                  <Badge 
                    status={capabilities?.conversationalAI ? 'success' : 'error'} 
                    text={capabilities?.conversationalAI ? 'Dostępne' : 'Niedostępne'} 
                  />
                  <div style={{ marginTop: '8px' }}>
                    <Text type="secondary">
                      Chatbot i asystent AI
                    </Text>
                  </div>
                </div>
              </Card>
            </Col>
            
            <Col xs={24} sm={12} md={8}>
              <Card title="Multimodalne AI" size="small">
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '48px', marginBottom: '16px' }}>
                    🎨
                  </div>
                  <Badge 
                    status={capabilities?.multiModal ? 'success' : 'error'} 
                    text={capabilities?.multiModal ? 'Dostępne' : 'Niedostępne'} 
                  />
                  <div style={{ marginTop: '8px' }}>
                    <Text type="secondary">
                      Analiza obrazów i dokumentów
                    </Text>
                  </div>
                </div>
              </Card>
            </Col>
          </Row>
        </TabPane>

        <TabPane tab="Statystyki Użycia" key="usage">
          <Row gutter={[16, 16]}>
            <Col xs={24} lg={12}>
              <Card title="Ogólne Statystyki" size="small">
                <Descriptions column={1}>
                  <Descriptions.Item label="Łączne Zapytania">
                    {usage?.totalRequests?.toLocaleString() || 0}
                  </Descriptions.Item>
                  <Descriptions.Item label="Zapytania Bielik">
                    {usage?.bielikRequests?.toLocaleString() || 0}
                  </Descriptions.Item>
                  <Descriptions.Item label="Zapytania OpenAI">
                    {usage?.openaiRequests?.toLocaleString() || 0}
                  </Descriptions.Item>
                  <Descriptions.Item label="Zapytania LM Studio">
                    {usage?.lmStudioRequests?.toLocaleString() || 0}
                  </Descriptions.Item>
                  <Descriptions.Item label="Średnia Tokenów/Zapytanie">
                    {usage?.averageTokensPerRequest || 0}
                  </Descriptions.Item>
                  <Descriptions.Item label="Łączne Tokeny">
                    {usage?.totalTokensProcessed?.toLocaleString() || 0}
                  </Descriptions.Item>
                </Descriptions>
              </Card>
            </Col>
            
            <Col xs={24} lg={12}>
              <Card title="Wydajność" size="small">
                <Descriptions column={1}>
                  <Descriptions.Item label="Średni Czas Odpowiedzi">
                    {performance?.averageResponseTime || 0}ms
                  </Descriptions.Item>
                  <Descriptions.Item label="Zapytania w Ostatniej Godzinie">
                    {performance?.lastHourRequests || 0}
                  </Descriptions.Item>
                  <Descriptions.Item label="Szczytowy Czas Odpowiedzi">
                    {performance?.peakResponseTime || 0}ms
                  </Descriptions.Item>
                  <Descriptions.Item label="Wskaźnik Sukcesu">
                    <Progress 
                      percent={performance?.successRate || 0} 
                      size="small"
                      strokeColor={performance?.successRate >= 95 ? '#52c41a' : '#faad14'}
                    />
                  </Descriptions.Item>
                  <Descriptions.Item label="Wskaźnik Błędów">
                    <Progress 
                      percent={performance?.errorRate || 0} 
                      size="small"
                      strokeColor="#f5222d"
                    />
                  </Descriptions.Item>
                </Descriptions>
              </Card>
            </Col>
          </Row>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default AIServicesConfig;
