import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic, Alert, Button, Spin, Tag, List, Progress } from 'antd';
import { 
  RobotOutlined, 
  WarningOutlined, 
  TrophyOutlined, 
  HeartOutlined,
  ThunderboltOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { request } from '@/request';

const AIInsights = () => {
  const [loading, setLoading] = useState(true);
  const [insights, setInsights] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchAIInsights();
  }, []);

  const fetchAIInsights = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await request.get({ entity: 'ai/dashboard-insights' });
      
      if (response.success) {
        setInsights(response.result);
      } else {
        setError('Błąd podczas ładowania insights AI');
      }
    } catch (err) {
      console.error('AI Insights error:', err);
      setError('<PERSON>e udało się załadować danych AI');
      notification.error({
        message: '❌ Błąd Ładowania AI Insights',
        description: 'Wystąpił problem z pobraniem danych AI. Spróbuj ponownie później.',
        duration: 5
      });
    } finally {
      setLoading(false);
    }
  };

  const getHealthScoreColor = (score) => {
    if (score >= 80) return '#52c41a';
    if (score >= 60) return '#faad14';
    if (score >= 40) return '#ff7a45';
    return '#ff4d4f';
  };

  const getUrgencyColor = (urgency) => {
    const colors = {
      'critical': '#ff4d4f',
      'high': '#ff7a45',
      'medium': '#faad14',
      'low': '#52c41a'
    };
    return colors[urgency] || '#1890ff';
  };

  if (loading) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '40px' }}>
          <Spin size="large" />
          <p style={{ marginTop: '16px' }}>Ładowanie AI Insights...</p>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <Alert
          message="Błąd AI Insights"
          description={error}
          type="error"
          showIcon
          action={
            <Button size="small" onClick={fetchAIInsights}>
              Spróbuj ponownie
            </Button>
          }
        />
      </Card>
    );
  }

  if (!insights) {
    return (
      <Card>
        <Alert
          message="Brak danych AI"
          description="Nie znaleziono danych do analizy AI"
          type="info"
          showIcon
        />
      </Card>
    );
  }

  return (
    <div>
      {/* Header */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col span={24}>
          <Card>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div>
                <h2 style={{ margin: 0, display: 'flex', alignItems: 'center' }}>
                  <RobotOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
                  AI Insights Dashboard
                </h2>
                <p style={{ margin: 0, color: '#666' }}>
                  Inteligentne analizy i rekomendacje dla Twojego biznesu HVAC
                </p>
              </div>
              <Button 
                icon={<ReloadOutlined />} 
                onClick={fetchAIInsights}
                type="primary"
              >
                Odśwież
              </Button>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Summary Statistics */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Sprzęt Krytyczny"
              value={insights.summary.criticalEquipment}
              prefix={<WarningOutlined style={{ color: '#ff4d4f' }} />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Serwis Wymagany"
              value={insights.summary.maintenanceDue}
              prefix={<ThunderboltOutlined style={{ color: '#faad14' }} />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Gorące Leady"
              value={insights.summary.hotLeads}
              prefix={<TrophyOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Ryzyko Churn"
              value={insights.summary.churnRiskClients}
              prefix={<HeartOutlined style={{ color: '#ff7a45' }} />}
              valueStyle={{ color: '#ff7a45' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Detailed Insights */}
      <Row gutter={[16, 16]}>
        {/* Critical Equipment */}
        <Col xs={24} lg={12}>
          <Card 
            title={
              <span>
                <WarningOutlined style={{ color: '#ff4d4f', marginRight: '8px' }} />
                Sprzęt Krytyczny
              </span>
            }
            extra={<Tag color="red">{insights.details.criticalEquipment.length}</Tag>}
          >
            <List
              dataSource={insights.details.criticalEquipment}
              renderItem={(equipment) => (
                <List.Item>
                  <List.Item.Meta
                    title={equipment.name}
                    description={
                      <div>
                        <div>Klient: {equipment.client?.name}</div>
                        <div style={{ marginTop: '4px' }}>
                          <Progress
                            percent={equipment.healthScore}
                            size="small"
                            strokeColor={getHealthScoreColor(equipment.healthScore)}
                            format={(percent) => `${percent}% Health`}
                          />
                        </div>
                      </div>
                    }
                  />
                </List.Item>
              )}
              locale={{ emptyText: 'Brak sprzętu krytycznego' }}
            />
          </Card>
        </Col>

        {/* Maintenance Due */}
        <Col xs={24} lg={12}>
          <Card 
            title={
              <span>
                <ThunderboltOutlined style={{ color: '#faad14', marginRight: '8px' }} />
                Wymagany Serwis
              </span>
            }
            extra={<Tag color="orange">{insights.details.maintenanceDue.length}</Tag>}
          >
            <List
              dataSource={insights.details.maintenanceDue}
              renderItem={(equipment) => (
                <List.Item>
                  <List.Item.Meta
                    title={equipment.name}
                    description={
                      <div>
                        <div>Klient: {equipment.client?.name}</div>
                        <div>Typ: {equipment.type}</div>
                        <div style={{ color: '#ff7a45' }}>
                          Zaplanowany: {equipment.nextMaintenanceDate ? 
                            new Date(equipment.nextMaintenanceDate).toLocaleDateString('pl-PL') : 
                            'Nie zaplanowany'
                          }
                        </div>
                      </div>
                    }
                  />
                </List.Item>
              )}
              locale={{ emptyText: 'Brak zaległego serwisu' }}
            />
          </Card>
        </Col>

        {/* Hot Leads */}
        <Col xs={24} lg={12}>
          <Card 
            title={
              <span>
                <TrophyOutlined style={{ color: '#52c41a', marginRight: '8px' }} />
                Gorące Leady
              </span>
            }
            extra={<Tag color="green">{insights.details.hotLeads.length}</Tag>}
          >
            <List
              dataSource={insights.details.hotLeads}
              renderItem={(opportunity) => (
                <List.Item>
                  <List.Item.Meta
                    title={opportunity.name}
                    description={
                      <div>
                        <div>Klient: {opportunity.client?.name}</div>
                        <div>Wartość: {opportunity.value?.toLocaleString('pl-PL')} PLN</div>
                        <div style={{ marginTop: '4px' }}>
                          <Tag color="green">Score: {opportunity.leadScore}/100</Tag>
                          <Tag color="blue">{opportunity.stage}</Tag>
                        </div>
                      </div>
                    }
                  />
                </List.Item>
              )}
              locale={{ emptyText: 'Brak gorących leadów' }}
            />
          </Card>
        </Col>

        {/* Churn Risk Clients */}
        <Col xs={24} lg={12}>
          <Card 
            title={
              <span>
                <HeartOutlined style={{ color: '#ff7a45', marginRight: '8px' }} />
                Ryzyko Utraty Klientów
              </span>
            }
            extra={<Tag color="orange">{insights.details.churnRiskClients.length}</Tag>}
          >
            <List
              dataSource={insights.details.churnRiskClients}
              renderItem={(client) => (
                <List.Item>
                  <List.Item.Meta
                    title={client.name}
                    description={
                      <div>
                        <div>Typ budynku: {client.buildingType}</div>
                        <div style={{ marginTop: '4px' }}>
                          <Progress
                            percent={Math.round(client.churnProbability * 100)}
                            size="small"
                            strokeColor="#ff7a45"
                            format={(percent) => `${percent}% Risk`}
                          />
                        </div>
                        <div style={{ marginTop: '4px' }}>
                          <Tag color="blue">Health: {client.healthScore}/100</Tag>
                        </div>
                      </div>
                    }
                  />
                </List.Item>
              )}
              locale={{ emptyText: 'Brak klientów wysokiego ryzyka' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Last Updated */}
      <Row style={{ marginTop: '16px' }}>
        <Col span={24}>
          <div style={{ textAlign: 'center', color: '#666', fontSize: '12px' }}>
            Ostatnia aktualizacja: {new Date(insights.lastUpdated).toLocaleString('pl-PL')}
          </div>
        </Col>
      </Row>
    </div>
  );
};

export default AIInsights;
