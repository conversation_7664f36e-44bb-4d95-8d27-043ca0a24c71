import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic, Progress, Button, Spin, Alert, Divider } from 'antd';
import { 
  BarChartOutlined, 
  TrophyOutlined, 
  HeartOutlined,
  ToolOutlined,
  ReloadOutlined,
  RiseOutlined,
  FallOutlined
} from '@ant-design/icons';
import { request } from '@/request';

const AIAnalytics = () => {
  const [loading, setLoading] = useState(true);
  const [analytics, setAnalytics] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchAnalytics();
  }, []);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await request.get({ entity: 'ai/analytics-summary' });
      
      if (response.success) {
        setAnalytics(response.result);
      } else {
        setError('Błąd podczas ładowania analityki AI');
      }
    } catch (err) {
      console.error('AI Analytics error:', err);
      setError('<PERSON><PERSON> uda<PERSON><PERSON> się załadować analityki AI');
      notification.error({
        message: '❌ Błąd Ładowania Analityki AI',
        description: 'Wystąpił problem z pobraniem danych analitycznych. Spróbuj ponownie później.',
        duration: 5
      });
    } finally {
      setLoading(false);
    }
  };

  const getHealthColor = (score) => {
    if (score >= 80) return '#52c41a';
    if (score >= 60) return '#faad14';
    if (score >= 40) return '#ff7a45';
    return '#ff4d4f';
  };

  const formatPercentage = (value) => {
    return `${Math.round(value * 100)}%`;
  };

  if (loading) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '40px' }}>
          <Spin size="large" />
          <p style={{ marginTop: '16px' }}>Ładowanie analityki AI...</p>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <Alert
          message="Błąd Analityki AI"
          description={error}
          type="error"
          showIcon
          action={
            <Button size="small" onClick={fetchAnalytics}>
              Spróbuj ponownie
            </Button>
          }
        />
      </Card>
    );
  }

  if (!analytics) {
    return (
      <Card>
        <Alert
          message="Brak danych analitycznych"
          description="Nie znaleziono danych do analizy"
          type="info"
          showIcon
        />
      </Card>
    );
  }

  const { equipment = {}, leads = {}, customers = {} } = analytics;

  return (
    <div>
      {/* Header */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col span={24}>
          <Card>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div>
                <h2 style={{ margin: 0, display: 'flex', alignItems: 'center' }}>
                  <BarChartOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
                  AI Analytics Dashboard
                </h2>
                <p style={{ margin: 0, color: '#666' }}>
                  Zaawansowana analityka AI dla optymalizacji biznesu HVAC
                </p>
              </div>
              <Button 
                icon={<ReloadOutlined />} 
                onClick={fetchAnalytics}
                type="primary"
              >
                Odśwież
              </Button>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Equipment Analytics */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col span={24}>
          <Card 
            title={
              <span>
                <ToolOutlined style={{ color: '#1890ff', marginRight: '8px' }} />
                Analityka Sprzętu HVAC
              </span>
            }
          >
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12} md={6}>
                <Statistic
                  title="Łączna liczba urządzeń"
                  value={equipment.totalEquipment || 0}
                  prefix={<ToolOutlined />}
                />
              </Col>
              <Col xs={24} sm={12} md={6}>
                <div>
                  <div style={{ marginBottom: '8px', fontWeight: '500' }}>
                    Średni Health Score
                  </div>
                  <Progress
                    type="circle"
                    percent={Math.round(equipment.avgHealthScore || 0)}
                    strokeColor={getHealthColor(equipment.avgHealthScore || 0)}
                    width={80}
                    format={(percent) => `${percent}%`}
                  />
                </div>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Statistic
                  title="Sprzęt Krytyczny"
                  value={equipment.criticalCount || 0}
                  valueStyle={{ color: '#ff4d4f' }}
                  prefix={<FallOutlined />}
                  suffix={`/ ${equipment.totalEquipment || 0}`}
                />
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Statistic
                  title="Sprzęt Zdrowy"
                  value={equipment.healthyCount || 0}
                  valueStyle={{ color: '#52c41a' }}
                  prefix={<RiseOutlined />}
                  suffix={`/ ${equipment.totalEquipment || 0}`}
                />
              </Col>
            </Row>

            <Divider />

            <Row gutter={[16, 16]}>
              <Col xs={24} md={12}>
                <div style={{ textAlign: 'center' }}>
                  <h4>Rozkład Stanu Sprzętu</h4>
                  <div style={{ display: 'flex', justifyContent: 'space-around', marginTop: '16px' }}>
                    <div>
                      <Progress
                        type="circle"
                        percent={equipment.totalEquipment ? 
                          Math.round((equipment.healthyCount / equipment.totalEquipment) * 100) : 0}
                        strokeColor="#52c41a"
                        width={60}
                      />
                      <div style={{ marginTop: '8px', fontSize: '12px' }}>Zdrowy</div>
                    </div>
                    <div>
                      <Progress
                        type="circle"
                        percent={equipment.totalEquipment ? 
                          Math.round(((equipment.totalEquipment - equipment.healthyCount - equipment.criticalCount) / equipment.totalEquipment) * 100) : 0}
                        strokeColor="#faad14"
                        width={60}
                      />
                      <div style={{ marginTop: '8px', fontSize: '12px' }}>Średni</div>
                    </div>
                    <div>
                      <Progress
                        type="circle"
                        percent={equipment.totalEquipment ? 
                          Math.round((equipment.criticalCount / equipment.totalEquipment) * 100) : 0}
                        strokeColor="#ff4d4f"
                        width={60}
                      />
                      <div style={{ marginTop: '8px', fontSize: '12px' }}>Krytyczny</div>
                    </div>
                  </div>
                </div>
              </Col>
              <Col xs={24} md={12}>
                <div style={{ padding: '16px', backgroundColor: '#f5f5f5', borderRadius: '8px' }}>
                  <h4 style={{ margin: '0 0 16px 0' }}>Rekomendacje AI</h4>
                  <ul style={{ margin: 0, paddingLeft: '16px' }}>
                    {equipment.criticalCount > 0 && (
                      <li>Natychmiastowa inspekcja {equipment.criticalCount} urządzeń krytycznych</li>
                    )}
                    {equipment.avgHealthScore < 70 && (
                      <li>Wdrożenie programu prewencyjnej konserwacji</li>
                    )}
                    <li>Optymalizacja harmonogramów serwisowych</li>
                    <li>Monitoring predykcyjny dla starszego sprzętu</li>
                  </ul>
                </div>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      {/* Lead Analytics */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col span={24}>
          <Card 
            title={
              <span>
                <TrophyOutlined style={{ color: '#52c41a', marginRight: '8px' }} />
                Analityka Leadów i Sprzedaży
              </span>
            }
          >
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12} md={6}>
                <Statistic
                  title="Łączne Okazje"
                  value={leads.totalOpportunities || 0}
                  prefix={<TrophyOutlined />}
                />
              </Col>
              <Col xs={24} sm={12} md={6}>
                <div>
                  <div style={{ marginBottom: '8px', fontWeight: '500' }}>
                    Średni Lead Score
                  </div>
                  <Progress
                    type="circle"
                    percent={Math.round(leads.avgLeadScore || 0)}
                    strokeColor="#1890ff"
                    width={80}
                    format={(percent) => `${percent}%`}
                  />
                </div>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Statistic
                  title="Gorące Leady"
                  value={leads.hotLeads || 0}
                  valueStyle={{ color: '#52c41a' }}
                  prefix={<RiseOutlined />}
                  suffix={`/ ${leads.totalOpportunities || 0}`}
                />
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Statistic
                  title="Zimne Leady"
                  value={leads.coldLeads || 0}
                  valueStyle={{ color: '#ff4d4f' }}
                  prefix={<FallOutlined />}
                  suffix={`/ ${leads.totalOpportunities || 0}`}
                />
              </Col>
            </Row>

            <Divider />

            <Row gutter={[16, 16]}>
              <Col xs={24} md={12}>
                <div style={{ textAlign: 'center' }}>
                  <h4>Jakość Leadów</h4>
                  <div style={{ display: 'flex', justifyContent: 'space-around', marginTop: '16px' }}>
                    <div>
                      <Progress
                        type="circle"
                        percent={leads.totalOpportunities ? 
                          Math.round((leads.hotLeads / leads.totalOpportunities) * 100) : 0}
                        strokeColor="#52c41a"
                        width={60}
                      />
                      <div style={{ marginTop: '8px', fontSize: '12px' }}>Gorące</div>
                    </div>
                    <div>
                      <Progress
                        type="circle"
                        percent={leads.totalOpportunities ? 
                          Math.round(((leads.totalOpportunities - leads.hotLeads - leads.coldLeads) / leads.totalOpportunities) * 100) : 0}
                        strokeColor="#faad14"
                        width={60}
                      />
                      <div style={{ marginTop: '8px', fontSize: '12px' }}>Średnie</div>
                    </div>
                    <div>
                      <Progress
                        type="circle"
                        percent={leads.totalOpportunities ? 
                          Math.round((leads.coldLeads / leads.totalOpportunities) * 100) : 0}
                        strokeColor="#ff4d4f"
                        width={60}
                      />
                      <div style={{ marginTop: '8px', fontSize: '12px' }}>Zimne</div>
                    </div>
                  </div>
                </div>
              </Col>
              <Col xs={24} md={12}>
                <div style={{ padding: '16px', backgroundColor: '#f5f5f5', borderRadius: '8px' }}>
                  <h4 style={{ margin: '0 0 16px 0' }}>Rekomendacje Sprzedażowe</h4>
                  <ul style={{ margin: 0, paddingLeft: '16px' }}>
                    {leads.hotLeads > 0 && (
                      <li>Priorytetowe traktowanie {leads.hotLeads} gorących leadów</li>
                    )}
                    {leads.avgLeadScore < 60 && (
                      <li>Optymalizacja procesu kwalifikacji leadów</li>
                    )}
                    <li>Automatyzacja nurturingu dla średnich leadów</li>
                    <li>A/B testing komunikatów sprzedażowych</li>
                  </ul>
                </div>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      {/* Customer Analytics */}
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Card 
            title={
              <span>
                <HeartOutlined style={{ color: '#ff7a45', marginRight: '8px' }} />
                Analityka Klientów
              </span>
            }
          >
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12} md={6}>
                <Statistic
                  title="Łączni Klienci"
                  value={customers.totalClients || 0}
                  prefix={<HeartOutlined />}
                />
              </Col>
              <Col xs={24} sm={12} md={6}>
                <div>
                  <div style={{ marginBottom: '8px', fontWeight: '500' }}>
                    Średni Health Score
                  </div>
                  <Progress
                    type="circle"
                    percent={Math.round(customers.avgHealthScore || 0)}
                    strokeColor={getHealthColor(customers.avgHealthScore || 0)}
                    width={80}
                    format={(percent) => `${percent}%`}
                  />
                </div>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Statistic
                  title="Zdrowi Klienci"
                  value={customers.healthyClients || 0}
                  valueStyle={{ color: '#52c41a' }}
                  prefix={<RiseOutlined />}
                  suffix={`/ ${customers.totalClients || 0}`}
                />
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Statistic
                  title="Ryzyko Churn"
                  value={customers.riskClients || 0}
                  valueStyle={{ color: '#ff4d4f' }}
                  prefix={<FallOutlined />}
                  suffix={`/ ${customers.totalClients || 0}`}
                />
              </Col>
            </Row>

            <Divider />

            <Row gutter={[16, 16]}>
              <Col xs={24} md={12}>
                <div>
                  <h4>Średnie Ryzyko Churn</h4>
                  <Progress
                    percent={Math.round((customers.avgChurnProbability || 0) * 100)}
                    strokeColor="#ff7a45"
                    format={(percent) => `${percent}%`}
                  />
                </div>
              </Col>
              <Col xs={24} md={12}>
                <div style={{ padding: '16px', backgroundColor: '#f5f5f5', borderRadius: '8px' }}>
                  <h4 style={{ margin: '0 0 16px 0' }}>Rekomendacje Retencji</h4>
                  <ul style={{ margin: 0, paddingLeft: '16px' }}>
                    {customers.riskClients > 0 && (
                      <li>Program retencji dla {customers.riskClients} klientów ryzyka</li>
                    )}
                    {customers.avgChurnProbability > 0.3 && (
                      <li>Wdrożenie proaktywnej obsługi klienta</li>
                    )}
                    <li>Personalizacja komunikacji z klientami</li>
                    <li>Program lojalnościowy dla długoterminowych klientów</li>
                  </ul>
                </div>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      {/* Footer */}
      <Row style={{ marginTop: '16px' }}>
        <Col span={24}>
          <div style={{ textAlign: 'center', color: '#666', fontSize: '12px' }}>
            Analityka wygenerowana: {new Date(analytics.generatedAt).toLocaleString('pl-PL')}
          </div>
        </Col>
      </Row>
    </div>
  );
};

export default AIAnalytics;
